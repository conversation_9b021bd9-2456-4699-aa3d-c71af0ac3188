# HMBL Core - Care Plans Controller API Documentation

## Overview
This Postman collection provides comprehensive testing for the CarePlansController in the HMBL Core application. The controller manages care plans and all their associated components including goals, interventions, problems, care team members, reviews, follow-ups, services, and notes.

## Base URL
- **Development**: `http://localhost:8080`
- **Production**: Update the `baseUrl` variable in Postman

## Authentication
**Note**: The current controller implementation doesn't show explicit authentication middleware, but you may need to add authentication headers depending on your application's auth setup.

## Collection Variables
The collection includes pre-configured variables for easy testing:
- `baseUrl`: Base URL for the API
- `memberID`: Sample member UUID
- `carePlanID`: Sample care plan UUID
- `goalID`: Sample goal UUID
- `interventionID`: Sample intervention UUID
- `problemID`: Sample problem UUID
- `teamMemberID`: Sample team member UUID
- `reviewID`: Sample review UUID
- `followUpID`: Sample follow-up UUID
- `serviceID`: Sample service UUID
- `noteID`: Sample note UUID
- `teamID`: Sample team UUID (for adding team members from team)

## API Endpoints

### 1. Care Plans
**Base Path**: `/api/members/{memberID}/careplans` and `/api/careplans/{carePlanID}`

#### Routes:
- `POST /api/members/{memberID}/careplans` - Create a new care plan
- `GET /api/members/{memberID}/careplans` - List all care plans for a member
- `GET /api/members/{memberID}/careplans?status={status}` - List care plans filtered by status
- `GET /api/careplans/{carePlanID}` - Get specific care plan
- `PUT /api/careplans/{carePlanID}` - Update care plan
- `DELETE /api/careplans/{carePlanID}` - Delete care plan

#### Care Plan Model:
```json
{
  "title": "Comprehensive Care Plan for Diabetes Management",
  "startDate": "2025-01-01T00:00:00Z",
  "lastReviewed": "2025-01-15T00:00:00Z",
  "nextReviewDate": "2025-04-01T00:00:00Z",
  "outcome": "Initial assessment completed",
  "status": "active"
}
```

### 2. Goals
**Base Path**: `/api/careplans/{carePlanID}/goals`

#### Routes:
- `POST /goals` - Create goal
- `GET /goals` - List goals
- `PUT /goals/{goalID}` - Update goal
- `DELETE /goals/{goalID}` - Delete goal

#### Goal Model:
```json
{
  "title": "Medication Adherence Goal",
  "description": "Improve medication adherence",
  "type": "health",
  "targetDate": "2025-06-01T00:00:00Z",
  "status": "active",
  "outcome": null,
  "objective": "Patient will take prescribed medications as directed",
  "measurementCriteria": "90% medication adherence rate",
  "achievabilityNote": "Patient has shown willingness to improve",
  "barriers": "Forgetfulness, complex medication schedule"
}
```

### 3. Interventions
**Base Path**: `/api/careplans/{carePlanID}/interventions`

#### Routes:
- `POST /interventions` - Create intervention
- `GET /interventions` - List interventions
- `PUT /interventions/{interventionID}` - Update intervention
- `DELETE /interventions/{interventionID}` - Delete intervention

#### Intervention Model:
```json
{
  "action": "Schedule weekly medication review",
  "responsibleParty": "Primary Care Nurse",
  "dueDate": "2025-02-01T00:00:00Z"
}
```

### 4. Problems
**Base Path**: `/api/careplans/{carePlanID}/problems`

#### Routes:
- `POST /problems` - Create problem
- `GET /problems` - List problems
- `PUT /problems/{problemID}` - Update problem
- `DELETE /problems/{problemID}` - Delete problem

#### Problem Model:
```json
{
  "icdCode": "I10",
  "description": "Essential (primary) hypertension",
  "clinicalNote": "BP remains elevated despite medication adjustment.",
  "status": "active",
  "dateIdentified": "2023-06-01T00:00:00Z",
  "source": "EHR import",
  "confirmedBy": "Dr. Smith, MD"
}
```

**Status Values**: `active` | `resolved` | `inactive`
**Source Values**: `EHR import` | `self-reported` | `care team`

### 5. Care Team Members
**Base Path**: `/api/careplans/{carePlanID}/team-members`

#### Routes:
- `POST /team-members` - Create individual team member
- `POST /team-members/from-team` - Add all navigators from a team to care plan
- `GET /team-members` - List team members
- `PUT /team-members/{memberID}` - Update team member
- `DELETE /team-members/{memberID}` - Delete team member

#### Care Team Member Model:
```json
{
  "userID": "123e4567-e89b-12d3-a456-426614174009",
  "name": "Dr. Sarah Johnson",
  "role": "Primary Care Physician",
  "contactInfo": "<EMAIL>, (555) 123-4567"
}
```

#### Add Team Members from Team:
**Endpoint**: `POST /team-members/from-team`

This endpoint allows you to add all navigators from an existing team to the care plan as care team members. It fetches the specified team, retrieves all its navigators, and creates CareTeamMember records for each navigator.

**Request Body**:
```json
{
  "teamID": "123e4567-e89b-12d3-a456-426614174009"
}
```

**Response**: Array of created CareTeamMember objects
```json
[
  {
    "id": "456e7890-e89b-12d3-a456-426614174010",
    "userID": "789e1234-e89b-12d3-a456-426614174011",
    "name": "John Navigator",
    "role": "Navigator",
    "contactInfo": "<EMAIL>",
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T10:30:00Z"
  }
]
```

**Features**:
- Automatically fetches team and all associated navigators
- Creates CareTeamMember records with role set to "Navigator"
- Uses navigator's email as contact info
- Creates timeline entries for each added team member
- Returns all created team members

### 6. Reviews
**Base Path**: `/api/careplans/{carePlanID}/reviews`

#### Routes:
- `POST /reviews` - Create review
- `GET /reviews` - List reviews
- `PUT /reviews/{reviewID}` - Update review
- `DELETE /reviews/{reviewID}` - Delete review

#### Review Model:
```json
{
  "reviewDate": "2025-01-20T00:00:00Z",
  "notes": "Patient showing good progress on medication adherence.",
  "reviewerName": "Dr. Sarah Johnson",
  "reviewerRole": "Primary Care Physician"
}
```

### 7. Follow-ups
**Base Path**: `/api/careplans/{carePlanID}/followups`

#### Routes:
- `POST /followups` - Create follow-up
- `GET /followups` - List follow-ups
- `GET /followups/{followUpID}` - Get specific follow-up
- `PUT /followups/{followUpID}` - Update follow-up
- `DELETE /followups/{followUpID}` - Delete follow-up

#### Follow-up Model:
```json
{
  "datetime": "2025-01-25T14:30:00Z",
  "type": "Phone",
  "outcome": "Reached",
  "notes": "Patient confirmed medication adherence improving.",
  "staffName": "Nurse Mary Smith",
  "staffRole": "Care Coordinator"
}
```

### 8. Services
**Base Path**: `/api/careplans/{carePlanID}/services`

#### Routes:
- `POST /services` - Create service
- `GET /services` - List services
- `GET /services/{serviceID}` - Get specific service
- `PUT /services/{serviceID}` - Update service
- `DELETE /services/{serviceID}` - Delete service

#### Service Model:
```json
{
  "cboName": "Community Health Partners",
  "staffName": "John Martinez",
  "addedBy": "Dr. Sarah Johnson",
  "status": "pending",
  "appointmentDate": "2025-02-05T10:00:00Z",
  "outcomeReasonType": null,
  "outcomeReasonDescription": null
}
```

### 9. Notes
**Base Path**: `/api/careplans/{carePlanID}/notes`

#### Routes:
- `POST /notes` - Create note
- `GET /notes` - List notes
- `GET /notes/{noteID}` - Get specific note
- `PUT /notes/{noteID}` - Update note
- `DELETE /notes/{noteID}` - Delete note

#### Note Model:
```json
{
  "title": "Patient Progress Update",
  "type": "progress",
  "subtitle": "Weekly Assessment",
  "msg": "Patient showing significant improvement in mobility exercises. Able to walk 50 feet without assistance.",
  "status": "active"
}
```

**Type Values**: `progress` | `assessment` | `communication` | `careplan` | `general`
**Status Values**: `active` | `archived` | `draft`

### 10. Timeline Items
**Base Path**: `/api/careplans/{carePlanID}/timeline-items`

#### Routes:
- `POST /timeline-items` - Create timeline item
- `GET /timeline-items` - List timeline items
- `GET /timeline-items/{timelineItemID}` - Get specific timeline item
- `PUT /timeline-items/{timelineItemID}` - Update timeline item
- `DELETE /timeline-items/{timelineItemID}` - Delete timeline item

#### Timeline Item Model:
```json
{
  "carepackageID": "care-plan-timeline",
  "title": "Care Plan Review Scheduled",
  "status": "scheduled",
  "desc": "Quarterly care plan review scheduled with care team",
  "visible": true,
  "memberId": "123e4567-e89b-12d3-a456-426614174000",
  "meta": {
    "data": {
      "review_type": "quarterly",
      "priority": "normal"
    }
  }
}
```

## Status Codes
- `200 OK` - Successful GET/PUT requests
- `201 Created` - Successful POST requests
- `204 No Content` - Successful DELETE requests
- `404 Not Found` - Resource not found
- `400 Bad Request` - Invalid request data
- `500 Internal Server Error` - Server error

## Usage Instructions

1. **Import Collection**: Import the `CarePlansController_Postman_Collection.json` file into Postman
2. **Set Variables**: Update the collection variables with your actual UUIDs and base URL
3. **Run Tests**: Execute requests in logical order (create care plan first, then add components)
4. **Environment Setup**: Consider creating different environments for dev/staging/production

## Testing Workflow

### Recommended Testing Order:
1. Create a Care Plan
2. Add Goals, Interventions, Problems
3. Add Care Team Members
4. Create Reviews and Follow-ups
5. Add Services
6. Add Notes
7. Test updates and deletions

### Sample Test Scenario:
1. Create care plan for member
2. Add health goal for medication adherence
3. Create intervention for medication review
4. Add problem (hypertension)
5. Add care team member (physician)
6. Create review documenting progress
7. Add follow-up call
8. Add community service referral
9. Add progress note documenting patient improvement

## Notes
- All UUIDs should be valid UUID v4 format
- Dates should be in ISO 8601 format
- The controller uses Fluent ORM with PostgreSQL
- All models include automatic `createdAt` and `updatedAt` timestamps
- Foreign key relationships are enforced (care plan must exist before adding components)

## Error Handling
The controller includes basic error handling:
- Returns 404 for non-existent resources
- Validates required parameters
- Uses Vapor's built-in error handling

For production use, consider adding:
- Input validation middleware
- Rate limiting
- Comprehensive error responses
- Logging and monitoring
